# Notice Notification System

## Overview

The Notice Notification System allows sending email and SMS notifications to society members when a new notice or circular is added. The system supports flexible targeting options including sending to all members, selected members, or specific member groups.

## Features

- **Multiple Notification Methods**: Email, SMS, or both
- **Flexible Member Targeting**: All members, selected members, or member groups
- **Automatic Member Discovery**: Retrieves active members based on scope
- **Error Handling**: Comprehensive logging and error handling
- **Integration**: Seamlessly integrated with existing notice creation workflow

## API Parameters

When adding a notice, you can include the following parameters to control notifications:

### Required Parameters (existing)
- `company_id`: Society ID
- `type`: 'notice' or 'circular'
- `subject`: Notice subject
- `body`: Notice content
- `effective_from`: Effective date
- `published_on`: Publication date

### New Notification Parameters
- `send_notification` (boolean): Whether to send notifications (default: false)
- `notification_method` (string): 'email', 'sms', or 'both' (default: 'both')
- `notification_scope` (string): 'all', 'selected', or 'group' (default: 'all')
- `selected_members` (array): Array of member IDs (required when scope is 'selected')
- `member_group` (string): Member type name like 'Primary', 'Tenant' (used when scope is 'group')

## Usage Examples

### 1. Send to All Members (Email and SMS)

```json
{
    "company_id": 1,
    "type": "notice",
    "subject": "Important Society Notice",
    "body": "This is an important notice for all members...",
    "effective_from": "2024-01-01",
    "published_on": "2024-01-01",
    "send_notification": true,
    "notification_method": "both",
    "notification_scope": "all"
}
```

### 2. Send to Selected Members (Email Only)

```json
{
    "company_id": 1,
    "type": "circular",
    "subject": "Committee Meeting Notice",
    "body": "Committee members are requested to attend...",
    "effective_from": "2024-01-01",
    "published_on": "2024-01-01",
    "send_notification": true,
    "notification_method": "email",
    "notification_scope": "selected",
    "selected_members": [1, 5, 10, 15]
}
```

### 3. Send to Member Group (SMS Only)

```json
{
    "company_id": 1,
    "type": "notice",
    "subject": "Primary Members Notice",
    "body": "This notice is specifically for primary members...",
    "effective_from": "2024-01-01",
    "published_on": "2024-01-01",
    "send_notification": true,
    "notification_method": "sms",
    "notification_scope": "group",
    "member_group": "Primary"
}
```

## Implementation Details

### Member Discovery Logic

The system uses the following logic to discover members for notifications:

1. **All Members**: Retrieves all active members from the society
2. **Selected Members**: Uses the provided member IDs array
3. **Member Group**: Filters members by member type (Primary, Tenant, etc.)

### Notification Flow

1. **Notice Creation**: Notice is first saved to the database
2. **Member Discovery**: System identifies target members based on scope
3. **Notification Loop**: For each member, sends notifications based on method
4. **Error Handling**: Logs any failures while continuing with other members

### Database Tables Used

- `chsone_societies_master`: Society information
- `chsone_members_master`: Member details
- `chsone_units_master`: Unit information
- `chsone_member_type_master`: Member types
- `chsone_notices`: Notice storage

### Email Template Integration

The system integrates with the existing email template system using the `NotificationTraits`:
- Uses `sendEmailFromTemplate()` method
- Includes member and society details in email data
- Supports email attachments if configured

### SMS Integration

SMS notifications use the existing SMS service:
- Configurable SMS gateway settings
- Formatted message with notice subject
- Mobile number validation

## Error Handling

The system includes comprehensive error handling:

- **Logging**: All errors are logged with context information
- **Graceful Degradation**: Failures for individual members don't stop the entire process
- **Validation**: Checks for required data before sending notifications
- **Fallback**: Continues processing even if some notifications fail

## Testing

Run the test suite to verify functionality:

```bash
php artisan test tests/Feature/NoticeNotificationTest.php
```

The test suite covers:
- Notice creation with notifications to all members
- Selective member notifications
- Member group targeting
- Different notification methods

## Configuration

Ensure the following environment variables are configured:

### Email Configuration
- `EMAIL_FROM_ONEAPP`: From email address
- `EMAIL_SERVICE_URL`: Email service endpoint

### SMS Configuration
- `SMS_EMAIL`: SMS service email
- `SMS_EMAIL_PASSWORD`: SMS service password
- `SMS_SENDER_ID`: SMS sender ID
- `SMS_SERVICE_NAME`: SMS service name
- `SMS_URL`: SMS service URL

## Monitoring and Logs

Monitor the system using Laravel logs:

```bash
tail -f storage/logs/laravel.log | grep "Notice notifications"
```

Key log events:
- Successful notification sending
- Member discovery results
- Individual notification failures
- Configuration issues

## Future Enhancements

Potential improvements for future versions:
- Push notification support
- Email template customization per notice type
- Scheduled notification delivery
- Notification delivery status tracking
- Member notification preferences
- Bulk notification status reporting
