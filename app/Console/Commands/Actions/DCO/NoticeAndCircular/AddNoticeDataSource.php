<?php

namespace App\Console\Commands\Actions\DCO\NoticeAndCircular;

use App\Console\Commands\Action;
use App\Http\Traits\NotificationTraits;

class AddNoticeDataSource extends Action
{
    use NotificationTraits;

    protected $signature = 'datasource:addNotice {flowId} {parentId} {input}';

    protected $description = 'Add Notice Data Source';

    public function apply()
    {
        $companyId = $this->input['company_id'];
        $type = $this->input['type'];
        $subject = $this->input['subject'];
        $body = $this->input['body'];
        $effective_from = $this->input['effective_from'];
        $published_on = $this->input['published_on'];
        $visibility = $this->input['visibility'] ?? '';

        // New notification parameters
        $sendNotification = $this->input['send_notification'] ?? true;
        $notificationMethod = $this->input['notification_method'] ?? 'both'; // 'email', 'sms', 'both'
        $notificationScope = $this->input['notification_scope'] ?? 'all'; // 'all', 'selected', 'group'
        $selectedMembers = $this->input['selected_members'] ?? []; // Array of member IDs
        $memberGroup = $this->input['member_group'] ?? null; // Group type like 'primary', 'tenant', etc.

        $company = $this->masterDB()->table('chsone_societies_master')
            ->select(
                'soc_id as id',
                'soc_type_id',
                'soc_name',
                'soc_reg_num',
                'soc_address_1',
                'soc_address_2',
                'soc_landmark',
                'soc_city_or_town',
                'soc_state',
                'soc_pincode',
                'status',
                'completed',
            )
            ->where('soc_id', $companyId)
            ->where('status', 1)->first();

        $company_initials = $this->getCompanyInitials($company->soc_name);
        
        $notice_ref_no = $this->tenantDB()->table('chsone_notices')->max('notice_ref_no');
        $ref_no = str_replace($company_initials, '', $notice_ref_no);
        // $ref_no = $ref_no + 1;
        // add the company initials to the ref no and keep the number of digits to 4
        $notice_ref_no = $company_initials . str_pad($ref_no, 4, '0', STR_PAD_LEFT);
        $fk_member_id = $this->input['fk_member_id'] ?? null;

        $obj = $this->tenantDB()->table('chsone_notices')
            ->insert([
                'notice_ref_no' => $notice_ref_no,
                'soc_id' => $companyId,
                'fk_member_id' => $fk_member_id,
                'type' => $type,
                'subject' => $subject,
                'body' => $body,
                'status' => 1,
                'effective_from' => $effective_from,
                'published_on' => $published_on,
                'visibility' => $visibility,
                'created_by' => $this->input['created_by'] ?? 0,
                'created_date' => date('Y-m-d H:i:s'),
                'updated_by' => $this->input['updated_by'] ?? 0,
                'updated_date' => date('Y-m-d H:i:s'),
            ]);

        // Send notifications if requested
        if ($sendNotification) {
            $this->sendNoticeNotifications(
                $companyId,
                $subject,
                $body,
                $type,
                $notificationMethod,
                $notificationScope,
                $selectedMembers,
                $memberGroup
            );
        }
        if($obj) {
            $this->status = 'success';
            $this->message = 'Notice added successfully';
            $this->statusCode = 200;
        } else {
            $this->status = 'error';
            $this->message = 'Failed to add notice';
            $this->statusCode = 400;
        }
    }

    /**
     * Send notifications to members based on scope and method
     */
    private function sendNoticeNotifications($companyId, $subject, $body, $type, $notificationMethod, $notificationScope, $selectedMembers, $memberGroup)
    {
        try {
            // Get members based on scope
            $members = $this->getMembersForNotification($companyId, $notificationScope, $selectedMembers, $memberGroup);

            if (empty($members)) {
                logger()->warning('No members found for notification', [
                    'company_id' => $companyId,
                    'scope' => $notificationScope,
                    'selected_members' => $selectedMembers,
                    'member_group' => $memberGroup
                ]);
                return;
            }

            // Send notifications to each member
            foreach ($members as $member) {
                $this->sendNotificationToMember($member, $subject, $body, $type, $notificationMethod);
            }

            logger()->info('Notice notifications sent successfully', [
                'company_id' => $companyId,
                'type' => $type,
                'members_count' => count($members),
                'method' => $notificationMethod
            ]);

        } catch (\Exception $e) {
            logger()->error('Failed to send notice notifications', [
                'error' => $e->getMessage(),
                'company_id' => $companyId,
                'type' => $type
            ]);
        }
    }

    /**
     * Get members based on notification scope
     */
    private function getMembersForNotification($companyId, $scope, $selectedMembers, $memberGroup)
    {
        $query = $this->tenantDB()->table('chsone_members_master as members')
            ->select(
                'members.id',
                'members.member_first_name',
                'members.member_last_name',
                'members.member_email_id',
                'members.member_mobile_number',
                'units.unit_flat_number',
                'units.soc_building_name',
                'member_type.member_type_name'
            )
            ->leftJoin('chsone_units_master as units', 'units.unit_id', '=', 'members.fk_unit_id')
            ->leftJoin('chsone_member_type_master as member_type', 'member_type.member_type_id', '=', 'members.member_type_id')
            ->where('members.soc_id', $companyId)
            ->where('members.status', 1);

        switch ($scope) {
            case 'all':
                // Send to all active members
                break;

            case 'selected':
                if (!empty($selectedMembers)) {
                    $query->whereIn('members.id', $selectedMembers);
                } else {
                    return []; // No members selected
                }
                break;

            case 'group':
                if ($memberGroup) {
                    $query->where('member_type.member_type_name', $memberGroup);
                } else {
                    // Default to primary members if no group specified
                    $query->where('member_type.member_type_name', 'Primary');
                }
                break;

            default:
                // Default to primary members
                $query->where('member_type.member_type_name', 'Primary');
                break;
        }

        return $query->get()->toArray();
    }

    /**
     * Send notification to individual member
     */
    private function sendNotificationToMember($member, $subject, $body, $type, $method)
    {
        try {
            // Prepare notification data
            $notificationData = [
                'member_name' => trim($member->member_first_name . ' ' . $member->member_last_name),
                'email' => $member->member_email_id,
                'mobile_number' => $member->member_mobile_number,
                'unit_flat_number' => $member->unit_flat_number,
                'soc_building_name' => $member->soc_building_name,
                'subject' => $subject,
                'body' => $body,
                'type' => $type,
                'title' => ucfirst($type) . ' Notification'
            ];

            // Send email notification
            if ($method === 'email' || $method === 'both') {
                if (!empty($member->member_email_id)) {
                    $this->sendEmailNotification($notificationData);
                }
            }

            // Send SMS notification
            if ($method === 'sms' || $method === 'both') {
                if (!empty($member->member_mobile_number)) {
                    $this->sendSMSNotification($notificationData);
                }
            }

        } catch (\Exception $e) {
            logger()->error('Failed to send notification to member', [
                'member_id' => $member->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send email notification
     */
    private function sendEmailNotification($data)
    {
        try {
            $emailData = [
                'email' => $data['email'],
                'member_name' => $data['member_name'],
                'unit_flat_number' => $data['unit_flat_number'],
                'soc_building_name' => $data['soc_building_name'],
                'subject' => $data['subject'],
                'body' => $data['body'],
                'type' => $data['type'],
                'title' => $data['title']
            ];

            $this->sendEmailFromTemplate($emailData);

        } catch (\Exception $e) {
            logger()->error('Failed to send email notification', [
                'email' => $data['email'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send SMS notification
     */
    private function sendSMSNotification($data)
    {
        try {
            $smsMessage = "New {$data['type']}: {$data['subject']}. Please check your society app for details.";

            $smsData = [
                'mobile_number' => $data['mobile_number'],
                'message' => $smsMessage,
                'member_name' => $data['member_name'],
                'type' => $data['type'],
                'subject' => $data['subject']
            ];

            $this->sendSMS($smsData);

        } catch (\Exception $e) {
            logger()->error('Failed to send SMS notification', [
                'mobile' => $data['mobile_number'],
                'error' => $e->getMessage()
            ]);
        }
    }
}