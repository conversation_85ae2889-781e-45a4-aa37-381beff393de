<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class NoticeNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test database connections
        config(['database.connections.tenant.database' => ':memory:']);
        config(['database.connections.master.database' => ':memory:']);
    }

    /** @test */
    public function it_can_add_notice_and_send_notifications_to_all_members()
    {
        // Arrange
        $companyId = 1;
        $this->createTestData($companyId);

        $input = [
            'company_id' => $companyId,
            'type' => 'notice',
            'subject' => 'Test Notice',
            'body' => 'This is a test notice body',
            'effective_from' => '2024-01-01',
            'published_on' => '2024-01-01',
            'visibility' => 'public',
            'send_notification' => true,
            'notification_method' => 'both',
            'notification_scope' => 'all',
            'created_by' => 1,
            'updated_by' => 1
        ];

        // Act
        $result = Artisan::call('datasource:addNotice', [
            'flowId' => 'test-flow',
            'parentId' => 0,
            'input' => json_encode($input)
        ]);

        // Assert
        $this->assertEquals(0, $result);
        
        // Verify notice was created
        $notice = DB::connection('tenant')->table('chsone_notices')
            ->where('soc_id', $companyId)
            ->where('subject', 'Test Notice')
            ->first();
            
        $this->assertNotNull($notice);
        $this->assertEquals('notice', $notice->type);
        $this->assertEquals('Test Notice', $notice->subject);
    }

    /** @test */
    public function it_can_send_notifications_to_selected_members()
    {
        // Arrange
        $companyId = 1;
        $this->createTestData($companyId);

        $input = [
            'company_id' => $companyId,
            'type' => 'circular',
            'subject' => 'Test Circular',
            'body' => 'This is a test circular body',
            'effective_from' => '2024-01-01',
            'published_on' => '2024-01-01',
            'send_notification' => true,
            'notification_method' => 'email',
            'notification_scope' => 'selected',
            'selected_members' => [1, 2], // Specific member IDs
            'created_by' => 1
        ];

        // Act
        $result = Artisan::call('datasource:addNotice', [
            'flowId' => 'test-flow',
            'parentId' => 0,
            'input' => json_encode($input)
        ]);

        // Assert
        $this->assertEquals(0, $result);
    }

    /** @test */
    public function it_can_send_notifications_to_member_group()
    {
        // Arrange
        $companyId = 1;
        $this->createTestData($companyId);

        $input = [
            'company_id' => $companyId,
            'type' => 'notice',
            'subject' => 'Group Notice',
            'body' => 'This is a notice for primary members only',
            'effective_from' => '2024-01-01',
            'published_on' => '2024-01-01',
            'send_notification' => true,
            'notification_method' => 'sms',
            'notification_scope' => 'group',
            'member_group' => 'Primary',
            'created_by' => 1
        ];

        // Act
        $result = Artisan::call('datasource:addNotice', [
            'flowId' => 'test-flow',
            'parentId' => 0,
            'input' => json_encode($input)
        ]);

        // Assert
        $this->assertEquals(0, $result);
    }

    private function createTestData($companyId)
    {
        // Create society
        DB::connection('master')->table('chsone_societies_master')->insert([
            'soc_id' => $companyId,
            'soc_name' => 'Test Society',
            'soc_reg_num' => 'TEST001',
            'soc_address_1' => 'Test Address',
            'soc_city_or_town' => 'Test City',
            'soc_state' => 'Test State',
            'soc_pincode' => '123456',
            'status' => 1,
            'completed' => 1
        ]);

        // Create member types
        DB::connection('tenant')->table('chsone_member_type_master')->insert([
            ['member_type_id' => 1, 'member_type_name' => 'Primary'],
            ['member_type_id' => 2, 'member_type_name' => 'Tenant']
        ]);

        // Create units
        DB::connection('tenant')->table('chsone_units_master')->insert([
            [
                'unit_id' => 1,
                'unit_flat_number' => '101',
                'soc_building_name' => 'A Block',
                'soc_building_floor' => '1',
                'is_allotted' => 1
            ],
            [
                'unit_id' => 2,
                'unit_flat_number' => '102',
                'soc_building_name' => 'A Block',
                'soc_building_floor' => '1',
                'is_allotted' => 1
            ]
        ]);

        // Create members
        DB::connection('tenant')->table('chsone_members_master')->insert([
            [
                'id' => 1,
                'soc_id' => $companyId,
                'fk_unit_id' => 1,
                'member_type_id' => 1,
                'member_first_name' => 'John',
                'member_last_name' => 'Doe',
                'member_email_id' => '<EMAIL>',
                'member_mobile_number' => '9876543210',
                'status' => 1
            ],
            [
                'id' => 2,
                'soc_id' => $companyId,
                'fk_unit_id' => 2,
                'member_type_id' => 1,
                'member_first_name' => 'Jane',
                'member_last_name' => 'Smith',
                'member_email_id' => '<EMAIL>',
                'member_mobile_number' => '9876543211',
                'status' => 1
            ]
        ]);
    }
}
