# Notice Notification Examples

This document provides practical examples of how to use the new notice notification functionality.

## API Endpoint

The existing API endpoint remains the same:
```
POST /api/admin/notices/add_notice
```

## Example 1: Send Notice to All Members via Email and SMS

```bash
curl -X POST "http://localhost/api/admin/notices/add_notice" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": 1,
    "type": "notice",
    "subject": "Important Society Meeting",
    "body": "Dear Members, We are organizing an important society meeting on 15th January 2024 at 7:00 PM in the society hall. All members are requested to attend.",
    "effective_from": "2024-01-10",
    "published_on": "2024-01-10",
    "visibility": "public",
    "send_notification": true,
    "notification_method": "both",
    "notification_scope": "all",
    "created_by": 1
  }'
```

## Example 2: Send Circular to Selected Committee Members via Email Only

```bash
curl -X POST "http://localhost/api/admin/notices/add_notice" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": 1,
    "type": "circular",
    "subject": "Committee Meeting Agenda",
    "body": "Dear Committee Members, Please find attached the agenda for our upcoming committee meeting scheduled for 20th January 2024.",
    "effective_from": "2024-01-15",
    "published_on": "2024-01-15",
    "visibility": "private",
    "send_notification": true,
    "notification_method": "email",
    "notification_scope": "selected",
    "selected_members": [1, 5, 10, 15, 20],
    "created_by": 1
  }'
```

## Example 3: Send Notice to Primary Members Only via SMS

```bash
curl -X POST "http://localhost/api/admin/notices/add_notice" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": 1,
    "type": "notice",
    "subject": "Maintenance Payment Reminder",
    "body": "Dear Primary Members, This is a reminder that your monthly maintenance payment is due by 31st January 2024. Please make the payment to avoid late fees.",
    "effective_from": "2024-01-25",
    "published_on": "2024-01-25",
    "visibility": "public",
    "send_notification": true,
    "notification_method": "sms",
    "notification_scope": "group",
    "member_group": "Primary",
    "created_by": 1
  }'
```

## Example 4: Add Notice Without Sending Notifications (Default Behavior)

```bash
curl -X POST "http://localhost/api/admin/notices/add_notice" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": 1,
    "type": "notice",
    "subject": "Water Supply Schedule",
    "body": "Water supply will be available from 6:00 AM to 10:00 AM and 6:00 PM to 10:00 PM daily.",
    "effective_from": "2024-01-01",
    "published_on": "2024-01-01",
    "visibility": "public",
    "created_by": 1
  }'
```

## JavaScript/Frontend Example

```javascript
// Function to send notice with notifications
async function addNoticeWithNotifications(noticeData) {
  try {
    const response = await fetch('/api/admin/notices/add_notice', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
        'X-CSRF-TOKEN': csrfToken
      },
      body: JSON.stringify(noticeData)
    });

    const result = await response.json();
    
    if (result.status === 'success') {
      console.log('Notice added successfully:', result.message);
      return result;
    } else {
      console.error('Failed to add notice:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Error adding notice:', error);
    throw error;
  }
}

// Example usage
const noticeData = {
  company_id: 1,
  type: 'notice',
  subject: 'Emergency Water Cut',
  body: 'Due to pipeline maintenance, water supply will be cut from 2 PM to 6 PM today.',
  effective_from: '2024-01-10',
  published_on: '2024-01-10',
  visibility: 'public',
  send_notification: true,
  notification_method: 'both',
  notification_scope: 'all',
  created_by: 1
};

addNoticeWithNotifications(noticeData)
  .then(result => {
    alert('Notice published and notifications sent successfully!');
  })
  .catch(error => {
    alert('Failed to publish notice: ' + error.message);
  });
```

## Response Format

All examples will return a standardized JSON response:

### Success Response
```json
{
  "status": "success",
  "status_code": 200,
  "message": "Notice added successfully",
  "data": [],
  "meta": [],
  "pointer": {
    "actionId": "base64_encoded_action_id"
  }
}
```

### Error Response
```json
{
  "status": "error",
  "status_code": 400,
  "message": "Failed to add notice",
  "data": [],
  "meta": [],
  "pointer": {
    "actionId": "base64_encoded_action_id"
  }
}
```

## Parameter Reference

### Required Parameters
- `company_id`: Society ID (integer)
- `type`: 'notice' or 'circular' (string)
- `subject`: Notice subject (string)
- `body`: Notice content (string)
- `effective_from`: Effective date (YYYY-MM-DD)
- `published_on`: Publication date (YYYY-MM-DD)

### Optional Notification Parameters
- `send_notification`: Enable notifications (boolean, default: false)
- `notification_method`: 'email', 'sms', or 'both' (string, default: 'both')
- `notification_scope`: 'all', 'selected', or 'group' (string, default: 'all')
- `selected_members`: Array of member IDs (array, required when scope is 'selected')
- `member_group`: Member type name (string, used when scope is 'group')
- `visibility`: 'public' or 'private' (string, optional)
- `created_by`: User ID who created the notice (integer, optional)

## Testing the Implementation

You can test the implementation using the provided test suite:

```bash
# Run all notice notification tests
php artisan test tests/Feature/NoticeNotificationTest.php

# Run specific test
php artisan test tests/Feature/NoticeNotificationTest.php --filter it_can_add_notice_and_send_notifications_to_all_members
```

## Monitoring Notifications

Check the Laravel logs to monitor notification delivery:

```bash
# Monitor notification logs
tail -f storage/logs/laravel.log | grep "Notice notifications"

# Check for errors
tail -f storage/logs/laravel.log | grep "Failed to send"
```
